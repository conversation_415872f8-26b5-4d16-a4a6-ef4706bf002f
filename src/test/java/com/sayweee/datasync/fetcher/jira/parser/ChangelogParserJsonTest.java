package com.sayweee.datasync.fetcher.jira.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class ChangelogParserJsonTest {

    @InjectMocks
    private ChangelogParser changelogParser;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    void testParseJsonNodeWithValidData() throws Exception {
        // Given
        String jsonData = """
            {
                "issueId": "295492",
                "changeHistories": [
                    {
                        "id": "3178445",
                        "author": {
                            "accountId": "712020:64a92fc3-8e94-4d47-97db-bc4be020c550",
                            "displayName": "Willian Molina Ibanez"
                        },
                        "created": *************,
                        "items": [
                            {
                                "field": "description",
                                "fieldtype": "jira",
                                "fieldId": "description",
                                "fromString": "Old description",
                                "toString": "New description"
                            }
                        ]
                    },
                    {
                        "id": "3175479",
                        "author": {
                            "accountId": "712020:03fe6607-82bc-4342-b7f3-dfd8bc63d111",
                            "displayName": "Thiago Fernandes Ribeiro"
                        },
                        "created": *************,
                        "items": [
                            {
                                "field": "status",
                                "fieldtype": "jira",
                                "fieldId": "status",
                                "fromString": "Open",
                                "toString": "In Progress"
                            }
                        ]
                    }
                ]
            }
            """;

        JsonNode changelogData = objectMapper.readTree(jsonData);

        // When
        List<ChangeLogEntity> result = changelogParser.parse(changelogData);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证第一个变更记录
        ChangeLogEntity change1 = result.get(0);
        assertEquals(3178445, change1.getChangeId());
        assertEquals("295492", change1.getKey());
        assertNull(change1.getProjectId()); // JsonNode 格式中没有项目信息
        assertEquals("712020:64a92fc3-8e94-4d47-97db-bc4be020c550", change1.getUserId());
        assertEquals("Willian Molina Ibanez", change1.getUsername());
        assertEquals("description", change1.getField());
        assertEquals("Old description", change1.getFromString());
        assertEquals("New description", change1.getToString());
        assertNull(change1.getFromValue()); // JsonNode 格式中没有 fromValue
        assertNull(change1.getToValue()); // JsonNode 格式中没有 toValue
        assertNotNull(change1.getCreated());
        assertNotNull(change1.getInDate());

        // 验证时间转换
        OffsetDateTime expectedTime1 = OffsetDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(*************L), ZoneOffset.UTC);
        assertEquals(expectedTime1, change1.getCreated());

        // 验证第二个变更记录
        ChangeLogEntity change2 = result.get(1);
        assertEquals(3175479, change2.getChangeId());
        assertEquals("295492", change2.getKey());
        assertEquals("712020:03fe6607-82bc-4342-b7f3-dfd8bc63d111", change2.getUserId());
        assertEquals("Thiago Fernandes Ribeiro", change2.getUsername());
        assertEquals("status", change2.getField());
        assertEquals("Open", change2.getFromString());
        assertEquals("In Progress", change2.getToString());

        // 验证时间转换
        OffsetDateTime expectedTime2 = OffsetDateTime.ofInstant(
            java.time.Instant.ofEpochMilli(*************L), ZoneOffset.UTC);
        assertEquals(expectedTime2, change2.getCreated());
    }

    @Test
    void testParseJsonNodeWithNullData() {
        // When
        List<ChangeLogEntity> result = changelogParser.parse((JsonNode) null);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseJsonNodeWithNoChangeHistories() throws Exception {
        // Given
        String jsonData = """
            {
                "issueId": "295492"
            }
            """;

        JsonNode changelogData = objectMapper.readTree(jsonData);

        // When
        List<ChangeLogEntity> result = changelogParser.parse(changelogData);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseJsonNodeWithEmptyChangeHistories() throws Exception {
        // Given
        String jsonData = """
            {
                "issueId": "295492",
                "changeHistories": []
            }
            """;

        JsonNode changelogData = objectMapper.readTree(jsonData);

        // When
        List<ChangeLogEntity> result = changelogParser.parse(changelogData);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testParseJsonNodeWithMissingFields() throws Exception {
        // Given - 缺少一些字段的数据
        String jsonData = """
            {
                "issueId": "295492",
                "changeHistories": [
                    {
                        "id": "3178445",
                        "created": *************,
                        "items": [
                            {
                                "field": "description",
                                "toString": "New description"
                            }
                        ]
                    }
                ]
            }
            """;

        JsonNode changelogData = objectMapper.readTree(jsonData);

        // When
        List<ChangeLogEntity> result = changelogParser.parse(changelogData);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        ChangeLogEntity change = result.get(0);
        assertEquals(3178445, change.getChangeId());
        assertEquals("295492", change.getKey());
        assertNull(change.getUserId()); // 没有 author 信息
        assertNull(change.getUsername()); // 没有 author 信息
        assertEquals("description", change.getField());
        assertNull(change.getFromString()); // 没有 fromString
        assertEquals("New description", change.getToString());
        assertNotNull(change.getCreated());
    }

    @Test
    void testParseJsonNodeWithInvalidChangeId() throws Exception {
        // Given - changeId 不是数字
        String jsonData = """
            {
                "issueId": "295492",
                "changeHistories": [
                    {
                        "id": "invalid-id",
                        "created": *************,
                        "items": [
                            {
                                "field": "description",
                                "toString": "New description"
                            }
                        ]
                    }
                ]
            }
            """;

        JsonNode changelogData = objectMapper.readTree(jsonData);

        // When
        List<ChangeLogEntity> result = changelogParser.parse(changelogData);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        ChangeLogEntity change = result.get(0);
        // 应该使用字符串的哈希值作为 changeId
        assertEquals(Math.abs("invalid-id".hashCode()), change.getChangeId());
    }

    @Test
    void testParseJsonNodeWithNoChangeId() throws Exception {
        // Given - 没有 changeId
        String jsonData = """
            {
                "issueId": "295492",
                "changeHistories": [
                    {
                        "author": {
                            "accountId": "user123",
                            "displayName": "Test User"
                        },
                        "created": *************,
                        "items": [
                            {
                                "field": "description",
                                "toString": "New description"
                            }
                        ]
                    }
                ]
            }
            """;

        JsonNode changelogData = objectMapper.readTree(jsonData);

        // When
        List<ChangeLogEntity> result = changelogParser.parse(changelogData);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());

        ChangeLogEntity change = result.get(0);
        // 应该生成一个哈希值作为 changeId
        assertNotNull(change.getChangeId());
        assertTrue(change.getChangeId() > 0);
    }
}
