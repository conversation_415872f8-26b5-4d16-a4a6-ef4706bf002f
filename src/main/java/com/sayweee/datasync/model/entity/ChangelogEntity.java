package com.sayweee.datasync.model.entity;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;

/**
 * Issue 变更日志实体类
 * 对应数据库表: weee_jira.issue_changelogs
 */
@Getter
@Setter
public class ChangelogEntity {

    /**
     * 变更ID
     */
    private Integer changeId;

    /**
     * Issue Key (如: PROJ-123)
     */
    private String key;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 变更创建时间 (带时区)
     */
    private OffsetDateTime created;

    /**
     * 变更字段名
     */
    private String field;

    /**
     * 变更前的值
     */
    private String fromValue;

    /**
     * 变更前的字符串表示
     */
    private String fromString;

    /**
     * 变更后的值
     */
    private String toValue;

    /**
     * 变更后的字符串表示
     */
    private String toString;

    /**
     * 数据入库时间 (不带时区)
     */
    private LocalDateTime inDate;
}
