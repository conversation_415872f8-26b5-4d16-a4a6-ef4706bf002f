package com.sayweee.datasync.service;


//import brave.Span;
//import brave.Tracer;

import com.sayweee.datasync.fetcher.jira.ChangelogFetcher;
import com.sayweee.datasync.fetcher.jira.IssueFetcher;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.model.entity.CommentEntity;
import com.sayweee.datasync.model.entity.IssueEntity;
import com.sayweee.datasync.model.entity.LinkedIssueEntity;
import com.sayweee.datasync.model.request.JiraSyncRequest;
import com.sayweee.datasync.writer.jira.CommentWriter;
import com.sayweee.datasync.writer.jira.IssueWriter;
import com.sayweee.datasync.writer.jira.LinkedIssueWriter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Stream;


@Slf4j
@Service
@RequiredArgsConstructor
public class JiraService {
    private final IssueFetcher issueFetcher;
    private final StreamBridge streamBridge;
    private final IssueWriter issueWriter;
    private final CommentWriter commentWriter;
    private final LinkedIssueWriter linkedIssueWriter;
    private final ChangelogFetcher changelogFetcher;
//    private final Tracer tracer;

    private static final String BINDING_NAME = "jira-sync-events-out-0";
    private AtomicInteger total = new AtomicInteger(0);

    public void syncIssues(String taskId, JiraSyncRequest request) {
//        Span currentSpan = tracer.currentSpan();
//        if (currentSpan != null) {
//            currentSpan.tag("jira.sync.taskId", taskId);
//            currentSpan.annotate("Sync job started");
//        }

//        String traceId = currentSpan != null ? currentSpan.context().traceIdString() : "N/A";

//        log.info("[TraceID: {}, TaskID: {}] Starting Jira sync job.", traceId, taskId);
        log.info("开始同步Jira任务: {}", taskId);
        Stream<JiraIngestionPayload> issuesStream = issueFetcher.getIssues(taskId, request);

        issuesStream.forEach(payload -> {
//            var metadata = new Metadata(
//                    UUID.randomUUID().toString(),
//                    "JiraIssueUpdated",
//                    Instant.now(),
//                    taskId,
//                    "1.0"
//            );

//            var event = new EventEnvelope<>(metadata, payload);
            streamBridge.send(BINDING_NAME, payload);
            total.incrementAndGet();
        });

//        if (currentSpan != null) {
//            currentSpan.annotate("Sync job finished");
//        }

        log.info("task [{}] synced successfully，total {}", taskId, total.get());
    }

    public void processAndSaveIssue(List<JiraIngestionPayload> events) {
//        List<IssueEntity> issueEntities = events.stream().map(event -> event.issue()).toList();
//        issueWriter.write(issueEntities);
//        log.info("saved {} issues", events.size());
//        List<CommentEntity> commentEntities = events.stream().flatMap(event -> event.comments().stream()).toList();
//        commentWriter.writeComments(commentEntities);
//        log.info("saved {} comments", commentEntities.size());

        List<LinkedIssueEntity> linkedIssueEntities = events.stream().flatMap(event -> event.linkedIssues().stream()).toList();
        linkedIssueWriter.write(linkedIssueEntities);
        log.info("saved {} linked issues", linkedIssueEntities.size());
    }

    public void processAndFetchRemoteLink(List<JiraIngestionPayload> events) {

    }

    public void processAndFetchChangelog(List<JiraIngestionPayload> events) {
        changelogFetcher.fetch("changelog-fetcher", events).forEach(group -> {
            log.info("fetched changelog group: {}", group);
        });
    }
}