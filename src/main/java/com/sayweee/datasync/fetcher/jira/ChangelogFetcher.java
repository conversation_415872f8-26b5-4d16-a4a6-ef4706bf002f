package com.sayweee.datasync.fetcher.jira;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.sayweee.core.framework.util.JacksonUtils;
import com.sayweee.datasync.fetcher.jira.dto.JiraIngestionPayload;
import com.sayweee.datasync.fetcher.jira.parser.ChangelogParser;
import com.sayweee.datasync.model.entity.ChangeLogEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

@Component
@Slf4j
@RequiredArgsConstructor
public class ChangelogFetcher {
    @Value("${jira.url:https://your-domain.atlassian.net}")
    private String JIRA_SERVER_URI;

    @Value("${jira.username:}")
    private String JIRA_EMAIL;

    @Value("${jira.token:}")
    private String JIRA_API_TOKEN;

    private final ChangelogParser changelogParser;

    private final HttpClient httpClient;

    private record ChangelogPageState(List<ChangeLogEntity> currentPageChangelogs, String nextPageToken) {
    }

    private record ChangelogPageResult(List<ChangeLogEntity> changelogs, String nextPageToken) {
    }


    public Stream<ChangeLogEntity> fetch(String taskId, List<JiraIngestionPayload> events) {
        var issueIdList = events
                .stream()
                .map(JiraIngestionPayload::issue)
                .map(issue -> issue.id().toString())
                .toList();
        log.info("Task [{}] - Starting to fetch JIRA changelogs for {} issues.", taskId, issueIdList.size());
        try {
            if (issueIdList.contains("295937")) {
                log.info("issueIdList contains 295937");
            }
            // 1. Fetch the initial page to bootstrap the stream.
            ChangelogPageResult initialResult = fetchChangelogPage(taskId, issueIdList, null);
            log.info("Task [{}] - Initial page fetch complete.", taskId);

            // 2. Create the initial state for the stream iterator.
            ChangelogPageState initialState = new ChangelogPageState(
                    initialResult == null ? Collections.emptyList() : initialResult.changelogs(),
                    initialResult == null ? null : initialResult.nextPageToken()
            );

            return Stream.iterate(
                            initialState,
                            state -> !state.currentPageChangelogs().isEmpty(),
                            currentState -> {
                                String nextToken = currentState.nextPageToken();
                                if (nextToken == null || nextToken.isBlank()) {
                                    log.info("Task [{}] - Reached the last page, no more nextPageToken.", taskId);
                                    return new ChangelogPageState(Collections.emptyList(), null);
                                }

                                log.debug("Task [{}] - Fetching next page of data. Token: {}", taskId, nextToken);
                                try {
                                    ChangelogPageResult nextResult = fetchChangelogPage(taskId, issueIdList, nextToken);
                                    return new ChangelogPageState(nextResult.changelogs(), nextResult.nextPageToken());
                                } catch (IOException | InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                    throw new RuntimeException("Task [" + taskId + "] - Failed to fetch subsequent page", e);
                                }
                            }
                    )
                    .flatMap(state -> state.currentPageChangelogs().stream());

        } catch (Exception e) {
            log.error("Task [{}] - Failed to fetch JIRA changelogs.", taskId, e);
            throw new RuntimeException("Task [" + taskId + "] - Failed to fetch JIRA changelogs", e);
        }
    }

    private ChangelogPageResult fetchChangelogPage(String taskId, List<String> issueIdList, String nextPageToken) throws IOException, InterruptedException {
        String url = JIRA_SERVER_URI + "/rest/api/3/changelog/bulkfetch";
        String jsonPayload = createJsonPayload(issueIdList, nextPageToken);
        String authHeader = "Basic " + Base64.getEncoder().encodeToString((JIRA_EMAIL + ":" + JIRA_API_TOKEN).getBytes(StandardCharsets.UTF_8));
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json")
                .header("Accept", "application/json")
                .header("Authorization", authHeader)
                .POST(HttpRequest.BodyPublishers.ofString(jsonPayload))
                .build();

        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() < 200 || response.statusCode() >= 300) {
            throw new IOException("Task [" + taskId + "] - Unexpected response code: " + response.statusCode() + "\nBody: " + response.body());
        }

        JsonNode responseNode = JacksonUtils.instance().readTree(response.body());
        List<ChangeLogEntity> changelogs = new ArrayList<>();
        if (responseNode.has("issueChangeLogs")) {
            for (JsonNode value : responseNode.get("issueChangeLogs")) {
                try {
                    changelogs.addAll(changelogParser.parse(value));
                } catch (Exception e) {
                    log.warn("Failed to convert JsonNode to ChangeLogEntity: {}", e.getMessage(), e);
                }
            }

            String token = responseNode.has("nextPageToken") ? responseNode.get("nextPageToken").asText(null) : null;
            boolean isLast = responseNode.has("isLast") && responseNode.get("isLast").asBoolean(false);

            return new ChangelogPageResult(changelogs, isLast ? null : token);
        }

        return null;
    }

    private String createJsonPayload(List<String> issueIdList, String nextPageToken) throws JsonProcessingException {
        ObjectNode payload = JacksonUtils.instance().createObjectNode();
        ArrayNode issueIdsArray = payload.putArray("issueIdsOrKeys");
        if (issueIdList != null) {
            issueIdList.forEach(issueIdsArray::add);
        }
        payload.put("maxResults", 100);
        if (nextPageToken != null && !nextPageToken.isBlank()) {
            payload.put("nextPageToken", nextPageToken);
        }
        return JacksonUtils.instance().writeValueAsString(payload);
    }
}